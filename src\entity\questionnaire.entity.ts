import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
  HasMany,
} from 'sequelize-typescript';

/**
 * 问卷状态枚举
 */
export enum QuestionnaireStatus {
  DRAFT = 'draft', // 草稿
  PUBLISHED = 'published', // 已发布
  CLOSED = 'closed', // 已关闭
}

/**
 * 星级模式枚举
 */
export enum StarMode {
  FIVE_STAR = 5, // 5星制
  TEN_STAR = 10, // 10星制
}

@Table({
  tableName: 'questionnaires',
  comment: '问卷表',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_questionnaire_school_month',
      fields: ['sso_school_id', 'month'],
    },
    {
      name: 'idx_questionnaire_status',
      fields: ['status'],
    },
    {
      name: 'idx_questionnaire_creator',
      fields: ['creator_user_id'],
    },
  ],
})
export class Questionnaire extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '问卷ID',
  })
  id?: number = undefined;

  @Column({
    type: DataType.STRING(200),
    allowNull: false,
    comment: '问卷标题',
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '问卷描述',
  })
  description: string;

  @Column({
    type: DataType.STRING(7),
    allowNull: false,
    comment: '问卷月份（YYYY-MM格式）',
  })
  month: string;

  @Column({
    type: DataType.ENUM(...Object.values(QuestionnaireStatus)),
    allowNull: false,
    defaultValue: QuestionnaireStatus.DRAFT,
    comment: '问卷状态',
  })
  status: QuestionnaireStatus;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: StarMode.FIVE_STAR,
    validate: {
      isIn: [[5, 10]],
    },
    comment: '星级模式（5星或10星）',
  })
  star_mode: StarMode;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否包含学校评价',
  })
  include_school_evaluation: boolean;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: 'SSO学校ID',
  })
  sso_school_id: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: true,
    comment: 'SSO学校名称',
  })
  sso_school_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '创建用户ID（SSO用户ID）',
  })
  creator_user_id: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '创建用户名称',
  })
  creator_user_name: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '问卷开始时间',
  })
  start_time: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '问卷结束时间',
  })
  end_time: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '问卷说明/须知',
  })
  instructions: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否允许匿名评价',
  })
  allow_anonymous: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '最大评价教师数量限制（0表示无限制）',
  })
  max_teachers_limit: number;

  @CreatedAt
  created_at: Date;

  @UpdatedAt
  updated_at: Date;

  // 关联关系
  @HasMany(() => require('./response.entity').Response)
  responses: any[];
}
