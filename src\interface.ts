/**
 * @description User-Service parameters
 */
export interface IUserOptions {
  uid: number;
}

// 问卷相关接口定义
export interface IQuestionnaireQuery {
  sso_school_id?: string;
  month?: string;
  status?: string;
  page?: number;
  limit?: number;
}

export interface IQuestionnaireListResponse {
  list: any[];
  total: number;
  page: number;
  limit: number;
}

export interface ISSoSchoolInfo {
  id: string;
  name: string;
  status: string;
}

// 响应相关接口定义
export interface IResponseListResponse {
  list: any[];
  total: number;
  page: number;
  limit: number;
}

export interface IResponseStatistics {
  questionnaire_id: number;
  total_responses: number;
  completed_responses: number;
  completion_rate: number;
  average_rating: number;
  teacher_evaluation_count: number;
}

export interface ISubmitResponseResult {
  response_id: number;
  questionnaire_id: number;
  submission_time: Date;
  total_average_score: number;
  teacher_count: number;
}

export interface IRatingConversion {
  star: number;
  score: number;
  description: string;
}

export interface IRatingInfo {
  five_star_mode: {
    description: string;
    conversion: string;
    ratings: IRatingConversion[];
  };
  ten_star_mode: {
    description: string;
    conversion: string;
    ratings: IRatingConversion[];
  };
}
