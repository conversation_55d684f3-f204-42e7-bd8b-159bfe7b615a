/**
 * @description User-Service parameters
 */
export interface IUserOptions {
  uid: number;
}

// 问卷相关接口定义
export interface IQuestionnaireQuery {
  sso_school_id?: string;
  month?: string;
  status?: string;
  page?: number;
  limit?: number;
}

export interface IQuestionnaireListResponse {
  list: any[];
  total: number;
  page: number;
  limit: number;
}

export interface ISSoSchoolInfo {
  id: string;
  name: string;
  status: string;
}
