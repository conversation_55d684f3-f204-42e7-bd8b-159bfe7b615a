/**
 * @description User-Service parameters
 */
export interface IUserOptions {
  uid: number;
}

// 问卷相关接口定义
export interface IQuestionnaireQuery {
  sso_school_id?: string;
  month?: string;
  status?: string;
  page?: number;
  limit?: number;
}

export interface IQuestionnaireListResponse {
  list: any[];
  total: number;
  page: number;
  limit: number;
}

export interface ISSoSchoolInfo {
  id: string;
  name: string;
  status: string;
}

// 响应相关接口定义
export interface IResponseListResponse {
  list: any[];
  total: number;
  page: number;
  limit: number;
}

export interface IResponseStatistics {
  questionnaire_id: number;
  total_responses: number;
  completed_responses: number;
  completion_rate: number;
  average_rating: number;
  teacher_evaluation_count: number;
}

export interface ISubmitResponseResult {
  response_id: number;
  questionnaire_id: number;
  submission_time: Date;
  total_average_score: number;
  teacher_count: number;
}

export interface IRatingConversion {
  star: number;
  score: number;
  description: string;
}

export interface IRatingInfo {
  five_star_mode: {
    description: string;
    conversion: string;
    ratings: IRatingConversion[];
  };
  ten_star_mode: {
    description: string;
    conversion: string;
    ratings: IRatingConversion[];
  };
}

// 统计分析相关接口定义
export interface ISchoolStatistics {
  sso_school_id: string;
  sso_school_name?: string;
  month?: string;
  total_responses: number;
  completed_responses: number;
  completion_rate: number;
  school_average_score: number;
  teacher_average_score: number;
  total_teachers_evaluated: number;
  response_trend?: ITrendData[];
  teacher_ranking?: ITeacherRanking[];
}

export interface ITeacherStatistics {
  sso_teacher_id: string;
  sso_teacher_name?: string;
  sso_teacher_subject?: string;
  sso_teacher_department?: string;
  month?: string;
  total_evaluations: number;
  average_score: number;
  recommendation_rate: number;
  score_distribution?: IScoreDistribution[];
  keyword_cloud?: IKeywordData[];
  evaluation_trend?: ITrendData[];
  detailed_scores?: IDetailedScores;
}

export interface ITeacherRanking {
  sso_teacher_id: string;
  sso_teacher_name: string;
  sso_teacher_subject: string;
  sso_teacher_department: string;
  average_score: number;
  evaluation_count: number;
  recommendation_rate: number;
  rank: number;
}

export interface ITrendData {
  month: string;
  total_responses?: number;
  completed_responses?: number;
  completion_rate?: number;
  avg_school_score?: number;
  avg_teacher_score?: number;
  evaluation_count?: number;
  average_score?: number;
  recommendation_rate?: number;
}

export interface IScoreDistribution {
  score_range: string;
  count: number;
  percentage: number;
}

export interface IKeywordData {
  word: string;
  count: number;
}

export interface IDetailedScores {
  teaching_quality: number;
  teaching_attitude: number;
  classroom_management: number;
  communication: number;
  professional_knowledge: number;
}

export interface ITeacherRankingResponse {
  list: ITeacherRanking[];
  total: number;
}

// 操作日志相关接口定义
export interface IOperationLogListResponse {
  list: any[];
  total: number;
  page: number;
  limit: number;
}

export interface IOperationLogStatistics {
  total_operations: number;
  success_operations: number;
  failed_operations: number;
  success_rate: number;
  avg_response_time: number;
  operation_trend: any[];
  module_distribution: any[];
  user_activity: any[];
}
